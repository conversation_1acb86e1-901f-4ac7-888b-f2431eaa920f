<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_manufacturer extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_manufacturer` (
         `manufacturer_id` INT NOT NULL DEFAULT 0,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()},
         PRIMARY KEY (`manufacturer_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_manufacturer`");
   }
}
