export interface ICategory extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   origin_title: string;
   content: string;
   image_path: string;
   is_active: number;
   sort_order: number;
   parent_id: number;
   group_id: number;
   url: string;
   meta_title: string;
   meta_description: string;
   meta_keywords: string;
}

export interface ICategoryStore {
   category_id?: number;
   is_active?: number;
   sort_order?: number;
   parent_id?: number;
   translate?: ITranslate[];
}

export interface IManufacturer extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   origin_title: string;
   content: string;
   image_path: string;
   is_active: number;
   is_competing: number;
   sort_order: number;
   url: string;
   meta_title: string;
   meta_description: string;
   meta_keywords: string;
}

export interface IManufacturerStore {
   manufacturer_id?: number;
   is_active?: number;
   sort_order?: number;
   translate?: ITranslate[];
}

export interface IProduct extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   origin_title: string;
   content: string;
   image_path: string;
   is_active: number;
   sort_order: number;
   url: string;
   meta_title: string;
   meta_description: string;
   meta_keywords: string;
   category_list: { id: number; title: string }[];
   manufacturer_list: { id: number; title: string }[];
   standard_list: { id: number; title: string; value: string; image_path: string }[];
   attr_list: { id: number; title: string }[];
   image_list: { id: number; product_id: number; image_path: string }[];
}

export interface IProductStore {
   product_id?: number;
   is_active?: number;
   sort_order?: number;
   product_category?: number[];
   translate?: ITranslate[];
}