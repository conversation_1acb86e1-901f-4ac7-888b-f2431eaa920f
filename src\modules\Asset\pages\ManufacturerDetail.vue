<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="manufacturer.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-if="language === 1"
                     v-model="manufacturer.title"
                     v-bind:rules="[appRules.required()]" />
                  <div
                     v-else
                     class="mb-[22px] flex h-9 items-center text-sm select-all px-2">
                     {{ manufacturer.title }}
                  </div>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="manufacturer.content"
                     v-bind:rules="[appRules.required()]"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="manufacturer.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.isCompeting") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="manufacturer.is_competing"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ manufacturer.is_competing ? t("app.yes") : t("app.no") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="manufacturer.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ manufacturer.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[manufacturer.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useCreateManufacturer, useGetManufacturerById, useUpdateManufacturer } from "../services/ManufacturerService";
import { IManufacturer, IManufacturerStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// manufacturer
const manufacturer = ref({
   is_active: 1,
   is_competing: 0
} as IManufacturer);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("app.createManufacturer") : t("app.manufacturerDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("ManufacturerDetail", title);

// services
const getManufacturerById = useGetManufacturerById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      manufacturer.value = { ...data };
   }
});
const updateManufacturer = useUpdateManufacturer();
const createManufacturer = useCreateManufacturer();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["manufacturer", "manufacturerById"] });

// loading
const isLoading = computed(() => getManufacturerById.isLoading.value);
const isPending = computed(() => createManufacturer.isPending.value || updateManufacturer.isPending.value);
const isError = computed(() => getManufacturerById.isError.value);

// handlers
const deleteManufacturerImage = async () => {
   return await deleteImage.mutateAsync({
      id: manufacturer.value.id,
      path: manufacturer.value.image_path,
      table: "asset_manufacturer"
   });
};

const uploadManufacturerImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "manufacturer"
   });
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteManufacturerImage();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: IManufacturerStore = {
      code: manufacturer.value.code,
      title: manufacturer.value.title,
      translate: [
         {
            language_id: language.value,
            content: manufacturer.value.content
         }
      ],
      is_active: manufacturer.value.is_active,
      is_competing: manufacturer.value.is_competing
   };

   try {
      if (imageUpload.value.length) {
         if (manufacturer.value.image_path) {
            await deleteManufacturerImage();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadManufacturerImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createManufacturer.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.manufacturerDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      } else {
         await updateManufacturer.mutateAsync({ id: manufacturer.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
