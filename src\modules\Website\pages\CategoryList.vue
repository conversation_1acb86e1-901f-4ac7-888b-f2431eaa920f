<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.categoryList") }}</v-card-title>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: ICategory) => $router.push({ name: 'website.categoryDetail', params: { id: item.id } })">
            <template v-slot:item.is_active="{ value }">
               <v-chip v-bind:color="value ? 'success' : undefined">
                  {{ value ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetCategoryAll } from "../services/CategoryService";
import { ICategory } from "../utils/types";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<ICategory>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.createDate"), key: "created_at", width: "250", format: true },
   { title: t("app.updateDate"), key: "updated_at", width: "250", format: true }
]);

const { data, isLoading } = useGetCategoryAll();
</script>
