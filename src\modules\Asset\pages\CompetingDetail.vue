<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton
               v-if="competing.id"
               v-bind:disabled="isLoading || isPending"
               prepend-icon="$trash"
               color="error"
               @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="competing.title"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="competing.content"
                     auto-grow
                     no-resize />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-row>
                     <v-col md="2">
                        <v-select
                           v-model="competing.currency"
                           v-bind:items="['TRY', 'USD', 'EUR']" />
                     </v-col>
                     <v-col md="10">
                        <NumberInput
                           v-model="competing.price"
                           v-bind:step="1" />
                     </v-col>
                  </v-row>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:extension>
            <v-card-title class="text-base">Muadil Ürünler</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.product") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-row>
                     <v-col md="12">
                        <SelectInput
                           v-model="productKey"
                           v-bind:items="productAll"
                           v-bind:loading="productLoading"
                           multiple
                           return-object>
                           <template v-slot:append>
                              <v-btn
                                 v-bind:disabled="!productKey"
                                 color="primary"
                                 density="default"
                                 variant="tonal"
                                 @click="addProductHandler">
                                 {{ t("app.add") }}
                              </v-btn>
                           </template>
                        </SelectInput>
                     </v-col>
                  </v-row>
                  <v-row no-gutters>
                     <v-col cols="12">
                        <DetailList
                           v-bind:data="competing.product_list"
                           v-bind:delete="deleteProductHandler"
                           v-bind:to="'/asset/product/'" />
                     </v-col>
                  </v-row>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:extension>
            <v-card-title class="text-base">Bağlantılar</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="competing.category_list"
                     v-bind:items="categoryAll"
                     v-bind:loading="categoryLoading"
                     multiple
                     return-object />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.manufacturer") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="competing.manufacturer_list"
                     v-bind:items="manufacturerAll"
                     v-bind:loading="manufacturerLoading"
                     multiple
                     return-object />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.attribute") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="competing.attr_list"
                     v-bind:items="attrAll"
                     v-bind:loading="attrLoading"
                     multiple
                     return-object />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.standard") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-row>
                     <v-col md="5">
                        <SelectInput
                           v-model="standardKey"
                           v-bind:items="standardAll"
                           v-bind:loading="standardLoading"
                           item-value="id"
                           return-object
                           @select="console.log($event)"
                           @update:model-value="selectHandler" />
                     </v-col>
                     <v-col md="7">
                        <v-text-field
                           v-model="standardVal"
                           v-bind:disabled="!standardKey"
                           ref="standardValElement"
                           @keydown.enter="addStandardHandler">
                           <template v-slot:append>
                              <v-btn
                                 color="primary"
                                 density="default"
                                 variant="tonal"
                                 @click="addStandardHandler">
                                 {{ t("app.add") }}
                              </v-btn>
                           </template>
                        </v-text-field>
                     </v-col>
                  </v-row>
                  <v-row no-gutters>
                     <v-col cols="12">
                        <DetailList
                           v-bind:data="competing.standard_list"
                           v-bind:delete="deleteStandardHandler"
                           v-bind:to="'/asset/standard/'"
                           title="title"
                           subtitle="value" />
                     </v-col>
                  </v-row>
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[competing.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import DetailList from "@/components/List/DetailList.vue";
import { useGetAttrAll } from "../services/AttrService";
import { useGetCategoryAll } from "../services/CategoryService";
import { useCreateCompeting, useDeleteCompeting, useGetCompetingById, useUpdateCompeting } from "../services/CompetingService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useGetManufacturerAll } from "../services/ManufacturerService";
import { useGetProductAll } from "../services/ProductService";
import { useGetStandardAll } from "../services/StandardService";
import { ICompeting, ICompetingStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const appStore = useAppStore();
const confirmStore = useConfirmStore();

// competing
const standardValElement = ref<HTMLInputElement>();
const competing = ref({
   standard_list: [] as any,
   product_list: [] as any,
   category_list: [] as any,
   manufacturer_list: [] as any,
   attr_list: [] as any
} as ICompeting);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const title = computed(() => (isCreate.value ? t("app.createCompeting") : t("app.competingDetail")));
const imageUpload = ref([] as File[]);
const standardKey = ref();
const standardVal = ref();
const productKey = ref();

// set breadcrumb
appStore.setBreadcrumb("CompetingDetail", title);

// services
const getCompetingById = useGetCompetingById({
   id: routeId,
   enabled: isEnabled,
   onSuccess: (item) => {
      competing.value = { ...item, standard_list: [...item.standard_list], product_list: [...item.product_list] };
      competing.value.price = formatMoney(competing.value.price);
   }
});
const updateCompeting = useUpdateCompeting();
const createCompeting = useCreateCompeting();
const deleteCompeting = useDeleteCompeting();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["competing", "competingById"] });

// relation services
const { data: productData, isPending: productLoading } = useGetProductAll();
const productAll = computed(() => productData.value?.filter((item) => !competing.value.product_list?.find((i) => i.id === item.id)));
const { data: categoryAll, isPending: categoryLoading } = useGetCategoryAll();
const { data: manufacturerAll, isPending: manufacturerLoading } = useGetManufacturerAll({ params: { competing: 1 } });
const { data: attrAll, isPending: attrLoading } = useGetAttrAll();
const { data: standardData, isPending: standardLoading } = useGetStandardAll();
const standardAll = computed(() => standardData.value?.filter((item) => !competing.value.standard_list?.find((i) => i.id === item.id)));

// loading
const isLoading = computed(() => getCompetingById.isLoading.value);
const isPending = computed(() => createCompeting.isPending.value || updateCompeting.isPending.value || deleteCompeting.isPending.value);
const isError = computed(() => getCompetingById.isError.value);
const isSuccess = computed(() => createCompeting.isSuccess.value);

// handlers
const deleteCompetingImage = async () => {
   return await deleteImage.mutateAsync({
      id: competing.value.id,
      path: competing.value.image_path,
      table: "competing"
   });
};

const uploadCompetingImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "competing/" + competing.value.id
   });
};

const selectHandler = () => {
   if (!standardKey.value) {
      standardVal.value = null;
   } else {
      setTimeout(() => {
         standardValElement.value?.focus();
      }, 50);
   }
};

const addProductHandler = () => {
   competing.value.product_list.push(...productKey.value);
   productKey.value = null;
};

const deleteProductHandler = (id: number) => {
   competing.value.product_list = competing.value.product_list.filter((item) => item.id !== id);
};

const addStandardHandler = () => {
   competing.value.standard_list.push({
      ...standardKey.value,
      value: standardVal.value
   });
   standardKey.value = null;
   standardVal.value = null;
};

const deleteStandardHandler = (id: number) => {
   competing.value.standard_list = competing.value.standard_list.filter((item) => item.id !== id);
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteCompetingImage();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("record.failed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteCompeting")
      });

      if (confirm) {
         await deleteCompeting.mutateAsync(competing.value.id);
         snackbarStore.add({ text: t("record.deleted") });
         router.push({ name: "asset.competingList" });
      }
   } catch {
      snackbarStore.add({ text: t("record.failed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: ICompetingStore = {
      id: competing.value.id,
      title: competing.value.title,
      content: competing.value.content,
      price: formatDecimal(competing.value.price),
      currency: competing.value.currency,
      competing_product: competing.value.product_list.map((item) => item.id),
      competing_category: competing.value.category_list.map((item) => item.id),
      competing_manufacturer: competing.value.manufacturer_list.map((item) => item.id),
      competing_attr: competing.value.attr_list.map((item) => item.id),
      competing_standard: competing.value.standard_list.map((item) => ({
         standard_id: item.id,
         value: item.value
      }))
   };

   try {
      if (imageUpload.value.length) {
         if (competing.value.image_path) {
            await deleteCompetingImage();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadCompetingImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createCompeting.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.competingDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("record.created") });
            }
         });
      } else {
         await updateCompeting.mutateAsync({ id: competing.value.id, ...payload });
         snackbarStore.add({ text: t("record.updated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("record.failed"), color: "error" });
   }
};
</script>
