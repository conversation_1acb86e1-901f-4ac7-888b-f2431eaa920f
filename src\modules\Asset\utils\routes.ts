import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const assetRoutes: RouteRecordRaw[] = [
   {
      path: "/asset",
      meta: {
         layout: DefaultLayout,
         module: "asset"
      },
      children: [
         {
            path: "product",
            meta: {
               title: i18n.global.t("app.product", 2),
               breadcrumb: i18n.global.t("app.productList")
            },
            children: [
               {
                  path: "",
                  name: "asset.productList",
                  component: getComponent(() => import("../pages/ProductList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.productDetail",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.productDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.productCreate",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createProduct")
                  }
               }
            ]
         },
         {
            path: "category",
            meta: {
               title: i18n.global.t("app.category", 2),
               breadcrumb: i18n.global.t("app.categoryList")
            },
            children: [
               {
                  path: "",
                  name: "asset.categoryList",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.categoryDetail",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.categoryDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.categoryCreate",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createCategory")
                  }
               }
            ]
         },
         {
            path: "manufacturer",
            meta: {
               title: i18n.global.t("app.manufacturer", 2),
               breadcrumb: i18n.global.t("app.manufacturerList")
            },
            children: [
               {
                  path: "",
                  name: "asset.manufacturerList",
                  component: getComponent(() => import("../pages/ManufacturerList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.manufacturerDetail",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.manufacturerDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.manufacturerCreate",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createManufacturer")
                  }
               }
            ]
         },
         {
            path: "standard",
            meta: {
               title: i18n.global.t("app.standard", 2),
               breadcrumb: i18n.global.t("app.standardList")
            },
            children: [
               {
                  path: "",
                  name: "asset.standardList",
                  component: getComponent(() => import("../pages/StandardList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.standardDetail",
                  component: getComponent(() => import("../pages/StandardDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.standardDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.standardCreate",
                  component: getComponent(() => import("../pages/StandardDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createStandard")
                  }
               }
            ]
         },
         {
            path: "attribute",
            meta: {
               title: i18n.global.t("app.attribute", 2),
               breadcrumb: i18n.global.t("app.attrList")
            },
            children: [
               {
                  path: "",
                  name: "asset.attrList",
                  component: getComponent(() => import("../pages/AttrList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.attrDetail",
                  component: getComponent(() => import("../pages/AttrDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detail")
                  }
               },
               {
                  path: "create",
                  name: "asset.attrCreate",
                  component: getComponent(() => import("../pages/AttrDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.create")
                  }
               }
            ]
         },
         {
            path: "competing",
            meta: {
               title: i18n.global.t("app.competing", 2),
               breadcrumb: i18n.global.t("app.competingList")
            },
            children: [
               {
                  path: "",
                  name: "asset.competingList",
                  component: getComponent(() => import("../pages/CompetingList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.competingDetail",
                  component: getComponent(() => import("../pages/CompetingDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.competingDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.competingCreate",
                  component: getComponent(() => import("../pages/CompetingDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createCompeting")
                  }
               }
            ]
         },
         {
            path: "component",
            meta: {
               title: i18n.global.t("app.component", 2),
               breadcrumb: i18n.global.t("app.componentList")
            },
            children: [
               {
                  path: "",
                  name: "asset.componentList",
                  component: getComponent(() => import("../pages/ComponentList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.componentDetail",
                  component: getComponent(() => import("../pages/ComponentDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.componentDetail")
                  }
               },
               {
                  path: "create",
                  name: "asset.componentCreate",
                  component: getComponent(() => import("../pages/ComponentDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createComponent")
                  }
               }
            ]
         }
      ]
   }
];
