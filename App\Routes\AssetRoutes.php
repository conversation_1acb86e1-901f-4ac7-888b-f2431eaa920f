<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Core\Middlewares\Mikro;
use App\Modules\Asset\Controllers\AttrController;
use App\Modules\Asset\Controllers\ImageController;
use App\Modules\Asset\Controllers\ProductController;
use App\Modules\Asset\Controllers\CategoryController;
use App\Modules\Asset\Controllers\StandardController;
use App\Modules\Asset\Controllers\CompetingController;
use App\Modules\Asset\Controllers\ComponentController;
use App\Modules\Asset\Controllers\ManufacturerController;

/** @var System\Router\Router $router */

// $router->prefix('v1/product/mikro')->middleware([Mikro::class])->group(function () use ($router) {
//    $router->post('/', function() {
//       echo "selam";
//    });
// });

// Image routes for image_path
$router->prefix('v1/image')->middleware([Auth::class])->group(function () use ($router) {
   $router->post('/create', [ImageController::class, 'uploadImage']);
   $router->post('/delete', [ImageController::class, 'deleteImage']);
});

// Product routes
$router->prefix('v1/asset/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->post('/', [ProductController::class, 'createProduct']);
   $router->put('/', [ProductController::class, 'updateProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});

// Competing routes
$router->prefix('v1/asset/competing')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CompetingController::class, 'getAllCompeting']);
   $router->post('/', [CompetingController::class, 'createCompeting']);
   $router->put('/', [CompetingController::class, 'updateCompeting']);
   $router->delete('/{id}', [CompetingController::class, 'deleteCompeting'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CompetingController::class, 'getCompeting'])->where(['id' => '([0-9]+)']);
});

// Category routes
$router->prefix('v1/asset/category')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CategoryController::class, 'getAllCategory']);
   $router->post('/', [CategoryController::class, 'createCategory']);
   $router->put('/', [CategoryController::class, 'updateCategory']);
   $router->delete('/{id}', [CategoryController::class, 'deleteCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CategoryController::class, 'getCategory'])->where(['id' => '([0-9]+)']);
});

// Manufacturer routes
$router->prefix('v1/asset/manufacturer')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ManufacturerController::class, 'getAllManufacturer']);
   $router->post('/', [ManufacturerController::class, 'createManufacturer']);
   $router->put('/', [ManufacturerController::class, 'updateManufacturer']);
   $router->delete('/{id}', [ManufacturerController::class, 'deleteManufacturer'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ManufacturerController::class, 'getManufacturer'])->where(['id' => '([0-9]+)']);
});

// Attribute routes
$router->prefix('v1/asset/attr')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [AttrController::class, 'getAllAttr']);
   $router->post('/', [AttrController::class, 'createAttr']);
   $router->put('/', [AttrController::class, 'updateAttr']);
   $router->delete('/{id}', [AttrController::class, 'deleteAttr'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [AttrController::class, 'getAttr'])->where(['id' => '([0-9]+)']);
});

// Standard routes
$router->prefix('v1/asset/standard')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [StandardController::class, 'getAllStandard']);
   $router->post('/', [StandardController::class, 'createStandard']);
   $router->put('/', [StandardController::class, 'updateStandard']);
   $router->delete('/{id}', [StandardController::class, 'deleteStandard'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [StandardController::class, 'getStandard'])->where(['id' => '([0-9]+)']);
});

// Component routes
$router->prefix('v1/asset/component')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ComponentController::class, 'getAllComponent']);
   $router->post('/', [ComponentController::class, 'createComponent']);
   $router->put('/', [ComponentController::class, 'updateComponent']);
   $router->delete('/{id}', [ComponentController::class, 'deleteComponent'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ComponentController::class, 'getComponent'])->where(['id' => '([0-9]+)']);
});
