<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_component_assignment extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_component_assignment` (
         `component_id` INT NOT NULL DEFAULT 0,
         `equipment_id` INT NOT NULL DEFAULT 0,
         `location_id` INT NOT NULL DEFAULT 0,
         `employee_id` INT NOT NULL DEFAULT 0,
         `quantity` INT NOT NULL DEFAULT 0,
         `status_id` INT NOT NULL DEFAULT 0,
         {$this->defaults()},
         PRIMARY KEY (`component_id`, `equipment_id`, `location_id`, `employee_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_component_assignment`");
   }
}
