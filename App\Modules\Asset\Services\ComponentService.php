<?php

declare(strict_types=1);

namespace App\Modules\Asset\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Asset\Resources\ComponentRequest;
use App\Modules\Asset\Repositories\ComponentRepository;

class ComponentService extends BaseService {
   /** @var ComponentRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ComponentRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAll(): array {
      $result = $this->repository->findAll();

      return array_map(function ($item) {
         $item['manufacturer'] = $this->repository->findManufacturer($item['manufacturer_id']);
         return $item;
      }, $result);
   }

   public function createComponent(ComponentRequest $dto): array {
      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'title' => 'required',
            'note' => 'nullable',
            'model' => 'nullable',
            'quantity' => 'required|numeric',
            'min_quantity' => 'required|numeric',
            'serial' => 'nullable',
            'manufacturer_id' => 'required|numeric',
            'location_id' => 'required|numeric',
            'supplier_id' => 'required|numeric',
            'order_serial' => 'nullable',
            'order_number' => 'nullable',
            'purchase_date' => 'nullable',
            'purchase_price' => 'numeric',
            'purchase_currency' => 'nullable',
            'warranty_duration' => 'nullable',
            'warranty_type' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $id = $this->create([
            'title' => $dto->title,
            'note' => $dto->note,
            'model' => $dto->model,
            'quantity' => $dto->quantity,
            'min_quantity' => $dto->min_quantity,
            'serial' => $dto->serial,
            'manufacturer_id' => $dto->manufacturer_id,
            'location_id' => $dto->location_id,
            'supplier_id' => $dto->supplier_id,
            'order_serial' => $dto->order_serial,
            'order_number' => $dto->order_number,
            'purchase_date' => $dto->purchase_date,
            'purchase_price' => $dto->purchase_price,
            'purchase_currency' => $dto->purchase_currency,
            'warranty_duration' => $dto->warranty_duration,
            'warranty_type' => $dto->warranty_type
         ]);

         if (isset($dto->image_path) && is_array($dto->image_path)) {
            foreach ($dto->image_path as $path) {
               $this->create([
                  'component_id' => $id,
                  'image_path' => $path
               ], 'asset_component_image');
            }
         }

         return $this->getOne($id);
      });
   }

   public function updateComponent(ComponentRequest $dto): array {
      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'note' => 'nullable',
            'model' => 'nullable',
            'quantity' => 'required|numeric',
            'min_quantity' => 'required|numeric',
            'serial' => 'nullable',
            'manufacturer_id' => 'required|numeric',
            'location_id' => 'required|numeric',
            'supplier_id' => 'required|numeric',
            'order_serial' => 'nullable',
            'order_number' => 'nullable',
            'purchase_date' => 'nullable',
            'purchase_price' => 'numeric',
            'purchase_currency' => 'nullable',
            'warranty_duration' => 'nullable',
            'warranty_type' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $this->update($dto, [
            'title' => $dto->title,
            'note' => $dto->note,
            'model' => $dto->model,
            'quantity' => $dto->quantity,
            'min_quantity' => $dto->min_quantity,
            'serial' => $dto->serial,
            'manufacturer_id' => $dto->manufacturer_id,
            'location_id' => $dto->location_id,
            'supplier_id' => $dto->supplier_id,
            'order_serial' => $dto->order_serial,
            'order_number' => $dto->order_number,
            'purchase_date' => $dto->purchase_date,
            'purchase_price' => $dto->purchase_price,
            'purchase_currency' => $dto->purchase_currency,
            'warranty_duration' => $dto->warranty_duration,
            'warranty_type' => $dto->warranty_type
         ], [
            'id' => $dto->id
         ]);

         if (isset($dto->image_path) && is_array($dto->image_path)) {
            // $this->repository->hardDelete([
            //    'component_id' => $dto->id
            // ], 'asset_component_image');

            foreach ($dto->image_path as $path) {
               $this->create([
                  'component_id' => $dto->id,
                  'image_path' => $path
               ], 'asset_component_image');
            }
         }

         return $this->getOne($dto->id);
      });
   }
}
