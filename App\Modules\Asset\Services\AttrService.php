<?php

declare(strict_types=1);

namespace App\Modules\Asset\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Resources\AttrRequest;
use App\Modules\Asset\Repositories\AttrRepository;

class AttrService extends BaseService {
   /** @var AttrRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      AttrRepository $repository
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createAttr(AttrRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required'
         ]);

         $id = $this->create([
            'sort_order' => $dto->sort_order
         ]);

         $this->translate($dto->translate, [
            'attr_id' => $id
         ], 'asset_attr_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateAttr(AttrRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required'
         ]);

         $this->update($dto, [
            'sort_order' => $dto->sort_order
         ], [
            'id' => $dto->id
         ]);

         $this->translate($dto->translate, [
            'attr_id' => $dto->id
         ], 'asset_attr_translate');

         return $this->getOne($dto->id, $lang_id);
      });
   }
}
