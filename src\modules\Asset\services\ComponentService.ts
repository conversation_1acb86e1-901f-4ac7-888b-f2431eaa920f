import { IComponent, IComponentStore } from "../utils/types";

export const useGetComponentAll = (payload?: TQuery<IComponent[]>) => {
   const options = computed(() => ({
      queryKey: ["component", "componentAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/component/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetComponentById = (payload?: { id?: MaybeRef<string> } & TQuery<IComponent>) => {
   const options = computed(() => ({
      queryKey: ["component", "componentById", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/component/${toValue(payload?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateComponent = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["component", "updateComponent"],
      mutationFn: async (data: IComponentStore): Promise<TResponse<IComponent>> => {
         return (await appAxios.put("/asset/component/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["component"] });
      }
   });
};

export const useCreateComponent = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["component", "createComponent"],
      mutationFn: async (data: IComponentStore): Promise<TResponse<IComponent>> => {
         return (await appAxios.post("/asset/component/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["component"] });
      }
   });
};

export const useDeleteComponent = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["component", "deleteComponent"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/asset/component/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["component"] });
      }
   });
};
