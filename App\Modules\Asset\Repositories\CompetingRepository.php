<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CompetingRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_competing'
   ) {
   }

   public function findCategory(int $competing_id): array {
      return $this->database
         ->prepare('SELECT
               asset_category.id,
               asset_category_translate.title
            FROM asset_competing_category

            JOIN asset_category ON asset_category.id = asset_competing_category.category_id
               AND asset_category.deleted_at IS NULL
            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_competing_category.category_id
               AND asset_category_translate.language_id = 1
            WHERE asset_competing_category.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id
         ])
         ->fetchAll();
   }

   public function findManufacturer(int $competing_id): array {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.id,
               asset_manufacturer.title
            FROM asset_competing_manufacturer

            JOIN asset_manufacturer ON asset_manufacturer.id = asset_competing_manufacturer.manufacturer_id
               AND asset_manufacturer.deleted_at IS NULL
            WHERE asset_competing_manufacturer.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id
         ])
         ->fetchAll();
   }

   public function findAttr(int $competing_id): array {
      return $this->database
         ->prepare('SELECT
               asset_attr.id,
               asset_attr_translate.title
            FROM asset_competing_attr

            JOIN asset_attr ON asset_attr.id = asset_competing_attr.attr_id
               AND asset_attr.deleted_at IS NULL
            LEFT JOIN asset_attr_translate ON asset_attr_translate.attr_id = asset_competing_attr.attr_id
               AND asset_attr_translate.language_id = 1
            WHERE asset_competing_attr.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id
         ])
         ->fetchAll();
   }

   public function findStandard(int $competing_id): array {
      return $this->database
         ->prepare('SELECT
               asset_standard.id,
               asset_standard_translate.title,
               asset_competing_standard.value,
               asset_standard.image_path
            FROM asset_competing_standard

            JOIN asset_standard ON asset_standard.id = asset_competing_standard.standard_id
               AND asset_standard.deleted_at IS NULL
            LEFT JOIN asset_standard_translate ON asset_standard_translate.standard_id = asset_competing_standard.standard_id
               AND asset_standard_translate.language_id = 1
            WHERE asset_competing_standard.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id
         ])
         ->fetchAll();
   }

   public function findProduct(int $competing_id): array {
      return $this->database
         ->prepare('SELECT
               asset_product.id,
               asset_product_translate.title,
               asset_product_translate.content
            FROM asset_competing_product

            JOIN asset_product ON asset_product.id = asset_competing_product.product_id
               AND asset_product.deleted_at IS NULL
            LEFT JOIN asset_product_translate ON asset_product_translate.product_id = asset_competing_product.product_id
               AND asset_product_translate.language_id = 1
            WHERE asset_competing_product.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id
         ])
         ->fetchAll();
   }
}
