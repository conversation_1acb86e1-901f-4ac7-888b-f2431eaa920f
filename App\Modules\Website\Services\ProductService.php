<?php

declare(strict_types=1);

namespace App\Modules\Website\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Website\Resources\ProductRequest;
use App\Modules\Website\Repositories\ProductRepository;

class ProductService extends BaseService {
   /** @var ProductRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ProductRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return array_map(function ($item) {
         $item['category_list'] = $this->repository->findCategory($item['id'], 1);
         $item['manufacturer_list'] = $this->repository->findManufacturer($item['id'], 1);
         $item['attr_list'] = $this->repository->findAttr($item['id'], 1);
         $item['standard_list'] = $this->repository->findStandard($item['id'], 1);
         $item['image_list'] = $this->repository->findImage($item['id']);

         return $item;
      }, $result);
   }

   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $result['category_list'] = $this->repository->findCategory($result['id'], 1);
      $result['manufacturer_list'] = $this->repository->findManufacturer($result['id'], 1);
      $result['attr_list'] = $this->repository->findAttr($result['id'], 1);
      $result['standard_list'] = $this->repository->findStandard($result['id'], 1);
      $result['image_list'] = $this->repository->findImage($result['id']);

      return $result;
   }

   public function overrideProduct(ProductRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'product_category' => 'required|must_be_array',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'url' => 'required',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required'
         ]);

         $check = $this->repository->findBy(['product_id' => $dto->product_id]);

         if (empty($check)) {
            $result = $this->repository->create([
               'product_id' => $dto->product_id,
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to create the web product record', 400);
            }
         } else {
            $result = $this->repository->update([
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
            ], [
               'product_id' => $dto->product_id
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to update the web product record', 400);
            }
         }

         $this->translate($dto->translate, [
            'product_id' => $dto->product_id,
         ], 'web_product_translate');

         return $this->getOne($dto->product_id, $lang_id);
      });
   }

   public function delete(array $where, ?string $table = null): bool {
      return $this->transaction(function () use ($where, $table) {
         $this->repository->hardDelete($where, $table);
         $this->repository->hardDelete($where, 'web_product_translate');

         return true;
      });
   }
}
