<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.componentList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'asset.componentDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: IComponent) => $router.push({ name: 'asset.componentDetail', params: { id: item.id } })" />
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetComponentAll } from "../services/ComponentService";
import { IComponent } from "../utils/types";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IComponent>[] => [
   { title: t("app.title"), key: "title" },
   { title: t("app.createDate"), key: "created_at", width: "250", format: true },
   { title: t("app.updateDate"), key: "updated_at", width: "250", format: true }
]);

const { data, isLoading } = useGetComponentAll();
</script>
