import { IAttr, IAttrStore } from "../utils/types";

export const useGetAttrAll = (payload?: TQuery<IAttr[]>) => {
   const options = computed(() => ({
      queryKey: ["attr", "attrAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/attr/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetAttrById = (payload?: { id?: MaybeRef<string> } & TQuery<IAttr>) => {
   const options = computed(() => ({
      queryKey: ["attr", "attrById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/attr/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateAttr = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "updateAttr"],
      mutationFn: async (data: IAttrStore): Promise<TResponse<IAttr>> => {
         return (await appAxios.put("/asset/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateAttr = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "createAttr"],
      mutationFn: async (data: IAttrStore): Promise<TResponse<IAttr>> => {
         return (await appAxios.post("/asset/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
