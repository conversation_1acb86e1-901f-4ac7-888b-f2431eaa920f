<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_product_standard extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_product_standard` (
         `standard_id` INT NOT NULL DEFAULT 0,
         `product_id` INT NOT NULL DEFAULT 0,
         `value` VARCHAR(150) NULL DEFAULT NULL,
         PRIMARY KEY (`standard_id`, `product_id`)
      )");
      // FOREIGN KEY (`standard_id`) REFERENCES `standard`(`id`),
      // FOREIGN KEY (`product_id`) REFERENCES `product`(`id`)

      $this->database->table('asset_product_standard')->insert([
         'standard_id' => 1,
         'product_id' => 1,
         'value' => '4244C'
      ])->prepare()->execute();

      $this->database->table('asset_product_standard')->insert([
         'standard_id' => 2,
         'product_id' => 1
      ])->prepare()->execute();

      $this->database->table('asset_product_standard')->insert([
         'standard_id' => 1,
         'product_id' => 2,
         'value' => '4531'
      ])->prepare()->execute();

      $this->database->table('asset_product_standard')->insert([
         'standard_id' => 2,
         'product_id' => 2
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_product_standard`");
   }
}
