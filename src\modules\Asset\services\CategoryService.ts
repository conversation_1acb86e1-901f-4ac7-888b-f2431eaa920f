import { ICategory, ICategoryStore } from "../utils/types";

export const useGetCategoryAll = (payload?: TQuery<ICategory[]>) => {
   const options = computed(() => ({
      queryKey: ["category", "categoryAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/category/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCategoryById = (payload?: { id?: MaybeRef<string> } & TQuery<ICategory>) => {
   const options = computed(() => ({
      queryKey: ["category", "categoryById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/category/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "updateCategory"],
      mutationFn: async (data: ICategoryStore): Promise<TResponse<ICategory>> => {
         return (await appAxios.put("/asset/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "createCategory"],
      mutationFn: async (data: ICategoryStore): Promise<TResponse<ICategory>> => {
         return (await appAxios.post("/asset/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
