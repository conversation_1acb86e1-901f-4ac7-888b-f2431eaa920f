import { IStandard, IStandardStore } from "../utils/types";

export const useGetStandardAll = (payload?: TQuery<IStandard[]>) => {
   const options = computed(() => ({
      queryKey: ["standard", "standardAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/standard/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetStandardById = (payload?: { id?: MaybeRef<string> } & TQuery<IStandard>) => {
   const options = computed(() => ({
      queryKey: ["standard", "standardById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/standard/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateStandard = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["standard", "updateStandard"],
      mutationFn: async (data: IStandardStore): Promise<TResponse<IStandard>> => {
         return (await appAxios.put("/asset/standard/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["standard"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateStandard = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["standard", "createStandard"],
      mutationFn: async (data: IStandardStore): Promise<TResponse<IStandard>> => {
         return (await appAxios.post("/asset/standard/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["standard"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
