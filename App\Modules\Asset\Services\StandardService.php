<?php

declare(strict_types=1);

namespace App\Modules\Asset\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Resources\StandardRequest;
use App\Modules\Asset\Repositories\StandardRepository;

class StandardService extends BaseService {
   /** @var StandardRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      StandardRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createStandard(StandardRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'image_path' => 'nullable',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $id = $this->create([
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $id
         ], 'asset_standard_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateStandard(StandardRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'image_path' => 'nullable',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $this->update($dto, [
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ], [
            'id' => $dto->id
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $dto->id
         ], 'asset_standard_translate');

         return $this->getOne($dto->id, $lang_id);
      });
   }
}
