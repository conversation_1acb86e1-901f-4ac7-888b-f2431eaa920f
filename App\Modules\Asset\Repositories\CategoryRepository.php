<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CategoryRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_category'
   ) {
   }

   // override
   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_category.*,
               asset_category_translate.*
            FROM asset_category

            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_category.id
               AND asset_category_translate.language_id = :language_id
            WHERE asset_category.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   // override
   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_category.*,
               asset_category_translate.*,
               COALESCE(asset_category_translate.title, default_translate.title) AS `title`,
               COALESCE(asset_category_translate.content, default_translate.content) AS `content`
            FROM asset_category

            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_category.id
               AND asset_category_translate.language_id = :language_id
            LEFT JOIN asset_category_translate AS default_translate ON default_translate.category_id = asset_category.id
               AND default_translate.language_id = 1
            WHERE asset_category.deleted_at IS NULL
               AND asset_category.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
