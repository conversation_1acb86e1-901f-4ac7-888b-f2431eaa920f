<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ManufacturerRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_manufacturer'
   ) {
   }

   // override
   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*,
               asset_manufacturer_translate.*
            FROM asset_manufacturer

            LEFT JOIN asset_manufacturer_translate ON asset_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND asset_manufacturer_translate.language_id = :language_id
            WHERE asset_manufacturer.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   // override
   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*,
               asset_manufacturer_translate.*,
               COALESCE(asset_manufacturer_translate.content, default_translate.content) AS `content`
            FROM asset_manufacturer

            LEFT JOIN asset_manufacturer_translate ON asset_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND asset_manufacturer_translate.language_id = :language_id
            LEFT JOIN asset_manufacturer_translate AS default_translate ON default_translate.manufacturer_id = asset_manufacturer.id
               AND default_translate.language_id = 1
            WHERE asset_manufacturer.deleted_at IS NULL
               AND asset_manufacturer.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findCompeting(int $lang_id, int $is_competing): array {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*,
               asset_manufacturer_translate.*
            FROM asset_manufacturer

            LEFT JOIN asset_manufacturer_translate ON asset_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND asset_manufacturer_translate.language_id = :language_id
            WHERE asset_manufacturer.deleted_at IS NULL
               AND asset_manufacturer.is_competing = :is_competing
         ')
         ->execute([
            'language_id' => $lang_id,
            'is_competing' => $is_competing
         ])
         ->fetchAll();
   }
}
