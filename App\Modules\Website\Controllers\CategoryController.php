<?php

declare(strict_types=1);

namespace App\Modules\Website\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Website\Services\CategoryService;
use App\Modules\Website\Resources\CategoryRequest;
use App\Modules\Website\Resources\CategoryResponse;

/**
 * @OA\Tag(name="Website Category", description="Website kategori işlemleri")
 */
class CategoryController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected CategoryService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Website Category"}, path="/category/", summary="Website kategori listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllCategory() {
      $this->response(function () {
         $result = $this->service->getAll($this->language());

         return array_map(function ($item) {
            $response = new CategoryResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(
    *    tags={"Website Category"}, path="/category/{id}", summary="Website kategori detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getCategory(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $response = new CategoryResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Put(
    *    tags={"Website Category"}, path="/category/", summary="Website kategori override güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="WEB-CAT001"),
    *       @OA\Property(property="image_path", type="string", example="/uploads/category.jpg"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="parent_id", type="integer", example=0),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Kategori Başlığı"),
    *          @OA\Property(property="content", type="string", example="Kategori açıklaması"),
    *          @OA\Property(property="url", type="string", example="kategori-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       )
    *    ))
    * )
    */
   public function overrideCategory() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new CategoryRequest();
         $request->fromArray($json);
         $result = $this->service->overrideCategory($request, $this->language());

         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Website Category"}, path="/category/{id}", summary="Website kategori override sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteCategory(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'category_id' => $id
         ]);

         return $result;
      });
   }
}
