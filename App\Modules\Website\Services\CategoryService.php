<?php

declare(strict_types=1);

namespace App\Modules\Website\Services;

use System\Upload\Upload;
use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Website\Resources\CategoryRequest;
use App\Modules\Website\Repositories\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Upload $upload,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function overrideCategory(CategoryRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'url' => 'required',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required'
         ]);

         $check = $this->repository->findBy(['category_id' => $dto->category_id]);

         if (empty($check)) {
            $result = $this->repository->create([
               'category_id' => $dto->category_id,
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
               'parent_id' => $dto->parent_id
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to create the web category record', 400);
            }
         } else {
            $result = $this->repository->update([
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
               'parent_id' => $dto->parent_id,
            ], [
               'category_id' => $dto->category_id
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to update the web category record', 400);
            }
         }

         $this->translate($dto->translate, [
            'category_id' => $dto->category_id,
         ], 'web_category_translate');

         return $this->getOne($dto->category_id, $lang_id);
      });
   }

   public function delete(array $where, ?string $table = null): bool {
      return $this->transaction(function () use ($where, $table) {
         $this->repository->hardDelete($where, $table);
         $this->repository->hardDelete($where, 'web_category_translate');

         return true;
      });
   }
}
