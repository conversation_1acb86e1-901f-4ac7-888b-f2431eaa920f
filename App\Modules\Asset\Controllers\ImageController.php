<?php

declare(strict_types=1);

namespace App\Modules\Asset\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Services\ImageService;

class ImageController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ImageService $service
   ) {
   }

   public function uploadImage() {
      $this->response(function () {
         $files = array_values($this->request->files());

         $path = $this->request->post('path');
         $result = $this->service->upload($files, $path);

         return $result;
      }, code: 201);
   }

   public function deleteImage() {
      $this->response(function () {
         $json = $this->request->json();

         $result = $this->service->unlink($json);

         return $result;
      });
   }
}
