<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_competing extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_competing` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `title` VARCHAR(250) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         `price` DECIMAL(10, 2) NOT NULL DEFAULT 0,
         `currency` VARCHAR(10) NOT NULL DEFAULT 'TRY',
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_competing`");
   }
}
