<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_competing_product extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_competing_product` (
         `competing_id` INT NOT NULL DEFAULT 0,
         `product_id` INT NOT NULL DEFAULT 0,
          PRIMARY KEY (`competing_id`, `product_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_competing_product`");
   }
}
