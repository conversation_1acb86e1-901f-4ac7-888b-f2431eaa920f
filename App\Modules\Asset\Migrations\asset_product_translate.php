<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_product_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_product_translate` (
         `product_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         {$this->defaults()},
         PRIMARY KEY (`product_id`, `language_id`)
      )");
      // FOREIGN KEY (`product_id`) REFERENCES `product`(`id`),
      // FOREIGN KEY (`language_id`) REFERENCES `language`(`id`)

      $this->database->table('asset_product_translate')->insert([
         'title' => 'Koruyucu Eldiven',
         'content' => 'Yüksek kaliteli koruyucu eldiven, EN388 standartlarına uygun.',
         'language_id' => 1,
         'product_id' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_product_translate')->insert([
         'title' => 'Protective Gloves',
         'content' => 'High quality protective gloves, compliant with EN388 standards.',
         'language_id' => 2,
         'product_id' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_product_translate')->insert([
         'title' => 'Normal Eldiven',
         'content' => 'EN420 standartlarına uygun eldiven',
         'language_id' => 1,
         'product_id' => 2,
      ])->prepare()->execute();

      $this->database->table('asset_product_translate')->insert([
         'title' => 'Normal Gloves',
         'content' => 'Gloves compliant with EN420 standards.',
         'language_id' => 2,
         'product_id' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_product_translate`");
   }
}
