<?php

declare(strict_types=1);

namespace App\Modules\Asset\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Resources\ManufacturerRequest;
use App\Modules\Asset\Repositories\ManufacturerRepository;

class ManufacturerService extends BaseService {
   /** @var ManufacturerRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ManufacturerRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1, ?array $request = null): array {
      if (isset($request['competing']) && ($request['competing'] === '1' || $request['competing'] === '0')) {
         $result = $this->repository->findCompeting($lang_id, (int) $request['competing']);
      } else {
         $result = $this->repository->findAll($lang_id);
      }

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createManufacturer(ManufacturerRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'title' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'is_competing' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric'
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'title' => $dto->title,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'is_competing' => $dto->is_competing,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'manufacturer_id' => $id
         ], 'asset_manufacturer_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateManufacturer(ManufacturerRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'title' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'is_competing' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric'
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'title' => $dto->title,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'is_competing' => $dto->is_competing,
            'sort_order' => $dto->sort_order,
         ], [
            'id' => $dto->id
         ]);

         $this->translate($dto->translate, [
            'manufacturer_id' => $dto->id,
         ], 'asset_manufacturer_translate');

         return $this->getOne($dto->id, $lang_id);
      });
   }
}
