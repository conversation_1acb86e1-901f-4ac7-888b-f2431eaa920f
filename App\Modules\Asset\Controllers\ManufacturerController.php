<?php

declare(strict_types=1);

namespace App\Modules\Asset\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Services\ManufacturerService;
use App\Modules\Asset\Resources\ManufacturerRequest;
use App\Modules\Asset\Resources\ManufacturerResponse;

/**
 * @OA\Tag(name="Manufacturer", description="Üretici işlemleri")
 */
class ManufacturerController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ManufacturerService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Manufacturer"}, path="/manufacturer/", summary="Üretici listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="competing", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllManufacturer() {
      $this->response(function () {
         $result = $this->service->getAll($this->language(), $this->request->get());

         return array_map(function ($item) {
            $response = new ManufacturerResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Manufacturer"}, path="/manufacturer/{id}", summary="Üretici detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getManufacturer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $manufacturer = new ManufacturerResponse();
         $manufacturer->fromArray($result);

         return $manufacturer;
      });
   }

   /**
    * @OA\Post(tags={"Manufacturer"}, path="/manufacturer/", summary="Üretici ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"code", "image_path", "is_active", "sort_order"},
    *       @OA\Property(property="code", type="string", example="MAN001"),
    *       @OA\Property(property="image_path", type="string", example="/images/manufacturers/manufacturer1.jpg"),
    *       @OA\Property(property="is_active", type="boolean", example=true),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Üretici Başlığı"),
    *          @OA\Property(property="content", type="string", example="Üretici Açıklaması")
    *       ))
    *    ))
    * )
    */
   public function createManufacturer() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ManufacturerRequest();
         $request->fromArray($json);
         $result = $this->service->createManufacturer($request, $this->language());

         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Manufacturer"}, path="/manufacturer/", summary="Üretici güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "code", "image_path", "is_active", "sort_order"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="MAN001"),
    *       @OA\Property(property="image_path", type="string", example="/images/manufacturers/manufacturer1.jpg"),
    *       @OA\Property(property="is_active", type="boolean", example=true),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Üretici Başlığı"),
    *          @OA\Property(property="content", type="string", example="Üretici Açıklaması")
    *       ))
    *    ))
    * )
    */
   public function updateManufacturer() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ManufacturerRequest();
         $request->fromArray($json);
         $result = $this->service->updateManufacturer($request, $this->language());

         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Manufacturer"}, path="/manufacturer/{id}", summary="Üretici sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteManufacturer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}
