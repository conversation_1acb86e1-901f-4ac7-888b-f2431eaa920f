<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_product'
   ) {
   }

   // override
   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_product.*,
               asset_product_translate.title,
               asset_product_translate.content,
               asset_product_translate.language_id
            FROM asset_product

            LEFT JOIN asset_product_translate ON asset_product_translate.product_id = asset_product.id
               AND asset_product_translate.language_id = :language_id
            WHERE asset_product.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   // override
   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_product.*,
               COALESCE(asset_product_translate.title, default_translate.title) AS `title`,
               COALESCE(asset_product_translate.content, default_translate.content) AS `content`,
               asset_product_translate.language_id
            FROM asset_product

            LEFT JOIN asset_product_translate ON asset_product_translate.product_id = asset_product.id
               AND asset_product_translate.language_id = :language_id
            LEFT JOIN asset_product_translate AS default_translate ON default_translate.product_id = asset_product.id
               AND default_translate.language_id = 1
            WHERE asset_product.deleted_at IS NULL
               AND asset_product.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findCategory(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               asset_category.id,
               asset_category_translate.title
            FROM asset_product_category

            JOIN asset_category ON asset_category.id = asset_product_category.category_id
               AND asset_category.deleted_at IS NULL
            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_product_category.category_id
               AND asset_category_translate.language_id = 1
            WHERE asset_product_category.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id
         ])
         ->fetchAll();
   }

   public function findManufacturer(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.id,
               asset_manufacturer.title
            FROM asset_product_manufacturer

            JOIN asset_manufacturer ON asset_manufacturer.id = asset_product_manufacturer.manufacturer_id
               AND asset_manufacturer.deleted_at IS NULL
            WHERE asset_product_manufacturer.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id
         ])
         ->fetchAll();
   }

   public function findAttr(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               asset_attr.id,
               asset_attr_translate.title
            FROM asset_product_attr

            JOIN asset_attr ON asset_attr.id = asset_product_attr.attr_id
               AND asset_attr.deleted_at IS NULL
            LEFT JOIN asset_attr_translate ON asset_attr_translate.attr_id = asset_product_attr.attr_id
               AND asset_attr_translate.language_id = 1
            WHERE asset_product_attr.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id
         ])
         ->fetchAll();
   }

   public function findStandard(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               asset_standard.id,
               asset_standard_translate.title,
               asset_product_standard.value,
               asset_standard.image_path
            FROM asset_product_standard

            JOIN asset_standard ON asset_standard.id = asset_product_standard.standard_id
               AND asset_standard.deleted_at IS NULL
            LEFT JOIN asset_standard_translate ON asset_standard_translate.standard_id = asset_product_standard.standard_id
               AND asset_standard_translate.language_id = 1
            WHERE asset_product_standard.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id
         ])
         ->fetchAll();
   }

   public function findImage(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               asset_product_image.id,
               asset_product_image.product_id,
               asset_product_image.image_path
            FROM asset_product_image
            WHERE asset_product_image.deleted_at IS NULL
               AND asset_product_image.image_path IS NOT NULL
               AND asset_product_image.product_id = :product_id
            ORDER BY asset_product_image.sort_order ASC, asset_product_image.created_at ASC
         ')
         ->execute([
            'product_id' => $product_id
         ])
         ->fetchAll();
   }
}
