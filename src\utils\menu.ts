export const registerMenu = async (): Promise<void> => {
   const appStore = useAppStore();
   appStore.setMenuLoading(true);
   await loadMenu()
      .then((menu) => {
         appStore.setMenu(menu);
      })
      .finally(() => {
         appStore.setMenuLoading(false);
      });
};

export const appMenu: TList[] = [
   {
      itemTitle: i18n.global.t("module.asset", 2),
      itemProps: {
         prependIcon: "$asset",
         value: "asset"
      }
   },
   {
      itemTitle: i18n.global.t("module.website", 2),
      itemProps: {
         prependIcon: "$website",
         value: "website"
      }
   },
   {
      itemTitle: i18n.global.t("module.dealer", 2),
      itemProps: {
         prependIcon: "$dealer",
         value: "dealer"
      }
   },
   {
      itemTitle: i18n.global.t("module.warehouse", 2),
      itemProps: {
         prependIcon: "$warehouse",
         value: "warehouse"
      }
   },
   {
      itemTitle: i18n.global.t("module.hand", 2),
      itemProps: {
         prependIcon: "$hand",
         value: "hand"
      }
   },
   {
      itemTitle: i18n.global.t("module.share", 2),
      itemProps: {
         prependIcon: "$share",
         value: "share"
      }
   },
   {
      itemTitle: i18n.global.t("module.loto", 2),
      itemProps: {
         prependIcon: "$loto",
         value: "loto"
      }
   }
];
