<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_product extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_product` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(150) NOT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('asset_product')->insert([
         'code' => 'PRD001',
         'is_active' => 1,
         'sort_order' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_product')->insert([
         'code' => 'PRD002',
         'is_active' => 1,
         'sort_order' => 1,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_product`");
   }
}
