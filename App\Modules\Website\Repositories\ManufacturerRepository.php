<?php

declare(strict_types=1);

namespace App\Modules\Website\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ManufacturerRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'web_manufacturer'
   ) {
   }

   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*,
               asset_manufacturer_translate.*,
               web_manufacturer_translate.*,
               COALESCE(web_manufacturer.is_active, asset_manufacturer.is_active) AS is_active,
               COALESCE(web_manufacturer.sort_order, asset_manufacturer.sort_order) AS sort_order,
               COALESCE(web_manufacturer_translate.title, asset_manufacturer.title) AS title,
               asset_manufacturer.title AS origin_title
            FROM asset_manufacturer

            LEFT JOIN web_manufacturer ON web_manufacturer.manufacturer_id = asset_manufacturer.id
               AND web_manufacturer.deleted_at IS NULL
            LEFT JOIN asset_manufacturer_translate ON asset_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND asset_manufacturer_translate.language_id = :language_id_inv
            LEFT JOIN web_manufacturer_translate ON web_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND web_manufacturer_translate.language_id = :language_id_web
            WHERE asset_manufacturer.deleted_at IS NULL
               AND asset_manufacturer.is_active = 1
               AND asset_manufacturer.is_competing = 0
         ')
         ->execute([
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*,
               asset_manufacturer_translate.*,
               web_manufacturer_translate.*,
               COALESCE(web_manufacturer.is_active, asset_manufacturer.is_active) AS is_active,
               COALESCE(web_manufacturer.sort_order, asset_manufacturer.sort_order) AS sort_order,
               COALESCE(web_manufacturer_translate.title, asset_manufacturer.title) AS title,
               asset_manufacturer.title AS origin_title
            FROM asset_manufacturer

            LEFT JOIN web_manufacturer ON web_manufacturer.manufacturer_id = asset_manufacturer.id
               AND web_manufacturer.deleted_at IS NULL
            LEFT JOIN asset_manufacturer_translate ON asset_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND asset_manufacturer_translate.language_id = :language_id_inv
            LEFT JOIN web_manufacturer_translate ON web_manufacturer_translate.manufacturer_id = asset_manufacturer.id
               AND web_manufacturer_translate.language_id = :language_id_web
            WHERE asset_manufacturer.deleted_at IS NULL
               AND asset_manufacturer.is_active = 1
               AND asset_manufacturer.is_competing = 0
               AND asset_manufacturer.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetch();
   }
}
