import { IManufacturer, IManufacturerStore } from "../utils/types";

export const useGetManufacturerAll = (payload?: TQuery<IManufacturer[]>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "website", "manufacturerAll"],
      queryFn: async () => {
         return (await appAxios.get("/website/manufacturer/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetManufacturerById = (payload?: { id?: MaybeRef<string> } & TQuery<IManufacturer>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "website", "manufacturerById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/website/manufacturer/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useOverrideManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "website", "overrideManufacturer"],
      mutationFn: async (data: IManufacturerStore): Promise<TResponse<IManufacturer>> => {
         return (await appAxios.put("/website/manufacturer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer", "website"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useDeleteManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "website", "deleteManufacturer"],
      mutationFn: async (data: { id: number }): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/website/manufacturer/${data.id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer", "website"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
