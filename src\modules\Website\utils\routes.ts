import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const websiteRoutes: RouteRecordRaw[] = [
   {
      path: "/website",
      meta: {
         layout: DefaultLayout,
         module: "website"
      },
      children: [
         {
            path: "product",
            meta: {
               title: "app.product",
               breadcrumb: "app.productList"
            },
            children: [
               {
                  path: "",
                  name: "website.productList",
                  component: getComponent(() => import("../pages/ProductList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.productDetail",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: "{ProductDetail}"
                  }
               }
            ]
         },
         {
            path: "category",
            meta: {
               title: "app.category",
               breadcrumb: "app.categoryList"
            },
            children: [
               {
                  path: "",
                  name: "website.categoryList",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.categoryDetail",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: "{CategoryDetail}"
                  }
               }
            ]
         },
         {
            path: "manufacturer",
            meta: {
               title: "app.manufacturer",
               breadcrumb: "app.manufacturerList"
            },
            children: [
               {
                  path: "",
                  name: "website.manufacturerList",
                  component: getComponent(() => import("../pages/ManufacturerList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.manufacturerDetail",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: "{ManufacturerDetail}"
                  }
               }
            ]
         }
      ]
   }
];
