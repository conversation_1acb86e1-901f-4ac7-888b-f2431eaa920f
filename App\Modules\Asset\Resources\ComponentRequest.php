<?php

declare(strict_types=1);

namespace App\Modules\Asset\Resources;

use App\Core\Abstracts\BaseResource;

class ComponentRequest extends BaseResource {
   public ?int $id;
   public string $title;
   public ?string $note;
   public ?string $model;
   public int $quantity = 0;
   public int $min_quantity = 0;
   public ?string $serial;
   public int $manufacturer_id = 0;
   public int $location_id = 0;
   public int $supplier_id = 0;
   public ?string $order_serial;
   public ?string $order_number;
   public ?string $purchase_date;
   public ?float $purchase_price = 0;
   public string $purchase_currency = 'TRY';
   public ?int $warranty_duration = 0;
   public string $warranty_type = 'year'; // day, month, year
   public ?array $image_path;
}
