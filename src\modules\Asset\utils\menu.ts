export const productMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: "module.asset"
   },
   {
      itemType: "divider"
   },
   {
      itemTitle: "app.product",
      itemProps: {
         to: "/asset/product",
         prependIcon: "$product"
      }
   },
   {
      itemTitle: "app.competing",
      itemProps: {
         to: "/asset/competing",
         prependIcon: "$competing"
      }
   },
   {
      itemTitle: "app.company",
      itemProps: {
         prependIcon: "$company",
      }
   },
   {
      itemTitle: "app.employee",
      itemProps: {
         prependIcon: "$employee"
      }
   },
   {
      itemTitle: "app.user",
      itemProps: {
         prependIcon: "$user"
      }
   },
   {
      itemTitle: "app.equipment",
      itemProps: {
         prependIcon: "$equipment"
      }
   },
   {
      itemTitle: "app.component",
      itemProps: {
         to: "/asset/component",
         prependIcon: "$component"
      }
   },
   {
      itemTitle: "app.license",
      itemProps: {
         prependIcon: "$license"
      }
   },
   {
      itemTitle: "app.subscription",
      itemProps: {
         prependIcon: "$subscription"
      }
   },
   {
      itemTitle: "app.definitions",
      itemProps: {
         prependIcon: "$definitions",
         value: "asset-definitions"
      },
      children: [
         {
            itemTitle: "app.category",
            itemProps: {
               to: "/asset/category"
            }
         },
         {
            itemTitle: "app.manufacturer",
            itemProps: {
               to: "/asset/manufacturer"
            }
         },
         {
            itemTitle: "app.standard",
            itemProps: {
               to: "/asset/standard"
            }
         },
         {
            itemTitle: "app.attribute",
            itemProps: {
               to: "/asset/attribute"
            }
         },
         {
            itemTitle: "app.department",
            itemProps: {
               to: "/asset/department"
            }
         },
         {
            itemTitle: "app.group",
            itemProps: {
               to: "/asset/group"
            }
         }
      ]
   }
];
