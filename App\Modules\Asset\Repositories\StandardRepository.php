<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class StandardRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_standard'
   ) {
   }

   // override
   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_standard.*,
               asset_standard_translate.*
            FROM asset_standard

            LEFT JOIN asset_standard_translate ON asset_standard_translate.standard_id = asset_standard.id
               AND asset_standard_translate.language_id = :language_id
            WHERE asset_standard.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   // override
   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_standard.*,
               asset_standard_translate.*,
               COALESCE(asset_standard_translate.title, default_translate.title) AS `title`,
               COALESCE(asset_standard_translate.content, default_translate.content) AS `content`
            FROM asset_standard

            LEFT JOIN asset_standard_translate ON asset_standard_translate.standard_id = asset_standard.id
               AND asset_standard_translate.language_id = :language_id
            LEFT JOIN asset_standard_translate AS default_translate ON default_translate.standard_id = asset_standard.id
               AND default_translate.language_id = 1
            WHERE asset_standard.deleted_at IS NULL
               AND asset_standard.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
