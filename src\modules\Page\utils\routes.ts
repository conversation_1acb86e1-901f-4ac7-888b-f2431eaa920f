import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const pageRoutes: RouteRecordRaw[] = [
   {
      path: "/page",

      meta: {
         title: "Test Page",
         breadcrumb: "Test Page",
         layout: DefaultLayout
      },

      children: [
         {
            path: ":id",
            component: getComponent(() => import("../pages/Page1.vue")),

            meta: {
               title: "Sub Page 1",
               breadcrumb: "Sub Page 1"
            }
         },
         {
            path: "page2",
            component: getComponent(() => import("../pages/Page2.vue"))
         }
      ]
   }
];
