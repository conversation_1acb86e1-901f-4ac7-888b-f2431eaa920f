<?php

declare(strict_types=1);

namespace App\Modules\Website\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Website\Services\ProductService;
use App\Modules\Website\Resources\ProductRequest;
use App\Modules\Website\Resources\ProductResponse;

/**
 * @OA\Tag(name="Website Product", description="Website ürün işlemleri")
 */
class ProductController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ProductService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Website Product"}, path="/product/", summary="Website ürün listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllProduct() {
      $this->response(function () {
         $result = $this->service->getAll($this->language());

         return array_map(function ($item) {
            $response = new ProductResponse();
            $response->fromArray($item);

            return $response;
         }, $result);

         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Website Product"}, path="/product/{id}", summary="Website ürün detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $response = new ProductResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Put(
    *    tags={"Website Product"}, path="/product/", summary="Website ürün güncelle/oluştur",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"product_id", "is_active", "sort_order", "translate", },
    *       @OA\Property(property="product_id", type="integer", example=1),
    *       @OA\Property(property="is_active", type="boolean", example=true),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Koruyucu Eldiven"),
    *          @OA\Property(property="url", type="string", example="koruyucu-eldiven"),
    *          @OA\Property(property="meta_title", type="string", example="Koruyucu Eldiven - Güvenlik Ekipmanları"),
    *          @OA\Property(property="meta_description", type="string", example="Yüksek kaliteli koruyucu eldiven"),
    *          @OA\Property(property="meta_keywords", type="string", example="eldiven, koruyucu, güvenlik")
    *       ))
    *    ))
    * )
    */
   public function overrideProduct() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ProductRequest();
         $request->fromArray($json);
         $result = $this->service->overrideProduct($request, $this->language());

         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Website Product"}, path="/product/{id}", summary="Website ürün sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'product_id' => $id
         ]);

         return $result;
      });
   }
}
