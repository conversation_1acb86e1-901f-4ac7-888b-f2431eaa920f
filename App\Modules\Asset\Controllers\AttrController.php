<?php

declare(strict_types=1);

namespace App\Modules\Asset\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Services\AttrService;
use App\Modules\Asset\Resources\AttrRequest;
use App\Modules\Asset\Resources\AttrResponse;

/**
 * @OA\Tag(name="Attr", description="Özellik işlemleri")
 */
class AttrController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected AttrService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Attr"}, path="/attr/", summary="Özellik listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllAttr() {
      $this->response(function () {
         $result = $this->service->getAll($this->language());

         return array_map(function ($item) {
            $response = new AttrResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Attr"}, path="/attr/{id}", summary="Özellik detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAttr(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $response = new AttrResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"Attr"}, path="/attr/", summary="Özellik ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"sort_order"},
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Antistatik")
    *       ))
    *    ))
    * )
    */
   public function createAttr() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new AttrRequest();
         $request->fromArray($json);
         $result = $this->service->createAttr($request, $this->language());

         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Attr"}, path="/attr/", summary="Özellik güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "sort_order"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Antistatik")
    *       ))
    *    ))
    * )
    */
   public function updateAttr() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new AttrRequest();
         $request->fromArray($json);
         $result = $this->service->updateAttr($request, $this->language());

         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Attr"}, path="/attr/{id}", summary="Özellik sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteAttr(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}
