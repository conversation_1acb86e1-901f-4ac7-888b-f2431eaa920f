<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_manufacturer_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_manufacturer_translate` (
         `manufacturer_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `content` TEXT NULL DEFAULT NULL,
         PRIMARY KEY (`manufacturer_id`, `language_id`)
      )");

      $this->database->table('asset_manufacturer_translate')->insert([
         'content' => 'ABC Güvenlik, iş güvenliği ekipmanları üreticisi.',
         'language_id' => 1,
         'manufacturer_id' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_manufacturer_translate')->insert([
         'content' => 'ABC Safety, manufacturer of work safety equipment.',
         'language_id' => 2,
         'manufacturer_id' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_manufacturer_translate')->insert([
         'content' => 'XYZ Güvenlik, iş güvenliği ekipmanları üreticisi.',
         'language_id' => 1,
         'manufacturer_id' => 2,
      ])->prepare()->execute();

      $this->database->table('asset_manufacturer_translate')->insert([
         'content' => 'XYZ Safety, manufacturer of work safety equipment.',
         'language_id' => 2,
         'manufacturer_id' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_manufacturer_translate`");
   }
}
