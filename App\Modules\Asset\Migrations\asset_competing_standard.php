<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_competing_standard extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_competing_standard` (
         `standard_id` INT NOT NULL DEFAULT 0,
         `competing_id` INT NOT NULL DEFAULT 0,
         `value` VARCHAR(150) NULL DEFAULT NULL,
         PRIMARY KEY (`standard_id`, `competing_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_competing_standard`");
   }
}
