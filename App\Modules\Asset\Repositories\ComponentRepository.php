<?php

declare(strict_types=1);

namespace App\Modules\Asset\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ComponentRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_component'
   ) {
   }

   public function findCategory(int $manufacturer_id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               asset_category.id,
               asset_category_translate.title
            FROM asset_category

            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_category.id
               AND asset_category_translate.language_id = :language_id
            WHERE asset_category.deleted_at IS NULL
               AND asset_category.manufacturer_id = :manufacturer_id
         ')
         ->execute([
            'manufacturer_id' => $manufacturer_id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findManufacturer(int $manufacturer_id): array|false {
      return $this->database
         ->prepare('SELECT
               asset_manufacturer.*
            FROM asset_manufacturer

            WHERE asset_manufacturer.id =
         ')
         ->execute([
            'manufacturer_id' => $manufacturer_id,
         ])
         ->fetch();
   }
}