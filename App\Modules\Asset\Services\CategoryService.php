<?php

declare(strict_types=1);

namespace App\Modules\Asset\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Resources\CategoryRequest;
use App\Modules\Asset\Repositories\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createCategory(CategoryRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id
         ]);

         $this->translate($dto->translate, [
            'category_id' => $id
         ], 'asset_category_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateCategory(CategoryRequest $dto, int $lang_id): array {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id
         ], [
            'id' => $dto->id
         ]);

         $this->translate($dto->translate, [
            'category_id' => $dto->id
         ], 'asset_category_translate');

         return $this->getOne($dto->id, $lang_id);
      });
   }
}
