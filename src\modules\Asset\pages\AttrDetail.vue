<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="attr.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="attr.title" />
                     </template>
                  </v-text-field>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useCreateAttr, useGetAttrById, useUpdateAttr } from "../services/AttrService";
import { IAttr, IAttrStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();

// attr
const attr = ref({} as IAttr);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => route.name === "asset.attrCreate");
const isDetail = computed(() => !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("app.create") : t("app.detail")));

// services
const getAttrById = useGetAttrById({
   id: routeId,
   enabled: isDetail,
   language: language,
   onSuccess: (data) => {
      attr.value = { ...data };
   }
});
const updateAttr = useUpdateAttr();
const createAttr = useCreateAttr();

// loading
const isLoading = computed(() => getAttrById.isLoading.value);
const isPending = computed(() => createAttr.isPending.value || updateAttr.isPending.value);
const isError = computed(() => getAttrById.isError.value);

// handlers
const formHandler = async () => {
   const payload: IAttrStore = {
      translate: [
         {
            language_id: language.value,
            title: attr.value.title
         }
      ]
   };

   try {
      if (isCreate.value) {
         await createAttr.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.attrDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("record.created") });
            }
         });
      } else {
         await updateAttr.mutateAsync({ id: attr.value.id, ...payload });
         snackbarStore.add({ text: t("record.updated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("record.failed"), color: "error" });
   }
};
</script>
