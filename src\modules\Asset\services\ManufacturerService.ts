import { IManufacturer, IManufacturerStore } from "../utils/types";

export const useGetManufacturerAll = (payload?: TQuery<IManufacturer[]>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "manufacturerAll", payload?.params?.competing],
      queryFn: async () => {
         return (await appAxios.get("/asset/manufacturer/", { params: { ...payload?.params } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetManufacturerById = (payload?: { id?: MaybeRef<string> } & TQuery<IManufacturer>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "manufacturerById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/manufacturer/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "updateManufacturer"],
      mutationFn: async (data: IManufacturerStore): Promise<TResponse<IManufacturer>> => {
         return (await appAxios.put("/asset/manufacturer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "createManufacturer"],
      mutationFn: async (data: IManufacturerStore): Promise<TResponse<IManufacturer>> => {
         return (await appAxios.post("/asset/manufacturer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
