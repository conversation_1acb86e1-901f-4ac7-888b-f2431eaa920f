import { IProduct, IProductStore } from "../utils/types";

export const useGetProductAll = (payload?: TQuery<IProduct[]>) => {
   const options = computed(() => ({
      queryKey: ["product", "website", "productAll"],
      queryFn: async () => {
         return (await appAxios.get("/website/product/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetProductById = (payload?: { id?: MaybeRef<string> } & TQuery<IProduct>) => {
   const options = computed(() => ({
      queryKey: ["product", "website", "productById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/website/product/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useOverrideProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "website", "overrideProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.put("/website/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product", "website"] });
      }
   });
};

export const useDeleteProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "website", "deleteProduct"],
      mutationFn: async (data: { id: number }): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/website/product/${data.id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product", "website"] });
      }
   });
};
